<?php
session_start();
require_once '../config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "Please log in as admin first.";
    exit;
}

echo "<h2>Request Debug Information</h2>";

// Check if request ID 13 exists
$requestId = 13;
echo "<h3>Checking Request ID: $requestId</h3>";

try {
    $stmt = $pdo->prepare("
        SELECT pr.*, m.full_name, m.first_name, m.last_name, m.email,
               COUNT(prr.id) as response_count
        FROM prayer_requests pr
        LEFT JOIN members m ON pr.member_id = m.id
        LEFT JOIN prayer_responses prr ON pr.id = prr.prayer_request_id
        WHERE pr.id = ?
        GROUP BY pr.id
    ");
    $stmt->execute([$requestId]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($request) {
        echo "<p><strong>Request found:</strong></p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $request['id'] . "</li>";
        echo "<li><strong>Title:</strong> " . htmlspecialchars($request['title']) . "</li>";
        echo "<li><strong>Status:</strong> " . $request['status'] . "</li>";
        echo "<li><strong>Member:</strong> " . htmlspecialchars($request['full_name']) . "</li>";
        echo "<li><strong>Response Count:</strong> " . $request['response_count'] . "</li>";
        echo "<li><strong>Allow Comments:</strong> " . ($request['allow_comments'] ?? 'NULL') . "</li>";
        echo "</ul>";
    } else {
        echo "<p><strong>Request ID $requestId not found!</strong></p>";
    }
    
    // Check database structure
    echo "<h3>Database Structure Check</h3>";
    
    // Check prayer_requests table structure
    echo "<h4>prayer_requests table columns:</h4>";
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_requests");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
    }
    echo "</ul>";
    
    // Check prayer_responses table structure
    echo "<h4>prayer_responses table columns:</h4>";
    $stmt = $pdo->query("SHOW COLUMNS FROM prayer_responses");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
    }
    echo "</ul>";
    
    // Check if there are any responses for this request
    if ($request) {
        echo "<h4>Responses for Request ID $requestId:</h4>";
        $stmt = $pdo->prepare("
            SELECT prr.*, 
                   CASE 
                       WHEN prr.is_admin_response = 1 OR prr.admin_id IS NOT NULL THEN 
                           COALESCE(a.full_name, a.username, 'Administrator')
                       ELSE 
                           COALESCE(m.full_name, CONCAT(m.first_name, ' ', m.last_name), 'Member')
                   END as commenter_name
            FROM prayer_responses prr
            LEFT JOIN members m ON prr.member_id = m.id
            LEFT JOIN admins a ON prr.admin_id = a.id
            WHERE prr.prayer_request_id = ?
            ORDER BY prr.created_at ASC
        ");
        $stmt->execute([$requestId]);
        $responses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($responses) {
            echo "<ul>";
            foreach ($responses as $response) {
                echo "<li>";
                echo "<strong>" . htmlspecialchars($response['commenter_name']) . "</strong> ";
                echo "(" . $response['response_type'] . ") ";
                echo "- " . htmlspecialchars($response['comment']);
                echo " <small>[" . $response['created_at'] . "]</small>";
                echo "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No responses found.</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

// Test JavaScript functionality
echo "<h3>JavaScript Test</h3>";
echo "<p>Test the fixed JavaScript parameter escaping:</p>";

if ($request) {
    echo "<div class='btn-group'>";
    echo "<button class='btn btn-outline-success btn-sm' onclick='showResponseModal(" . $request['id'] . ", " . json_encode($request['title']) . ", " . json_encode($request['full_name']) . ")' title='Test Response Modal'>";
    echo "<i class='bi bi-chat-dots'></i> Test Response Modal";
    echo "</button>";
    echo "<button class='btn btn-outline-danger btn-sm' onclick='deleteRequest(" . $request['id'] . ", " . json_encode($request['title']) . ")' title='Test Delete'>";
    echo "<i class='bi bi-trash'></i> Test Delete";
    echo "</button>";
    echo "</div>";

    echo "<p><small>These buttons use the same JavaScript parameter escaping as the fixed admin requests page.</small></p>";
}

echo "<script>
function showResponseModal(requestId, requestTitle, memberName) {
    console.log('showResponseModal called with:', requestId, requestTitle, memberName);
    alert('Response Modal function called successfully!\\nID: ' + requestId + '\\nTitle: ' + requestTitle + '\\nMember: ' + memberName);
}

function deleteRequest(requestId, title) {
    console.log('deleteRequest called with:', requestId, title);
    if (confirm('Are you sure you want to delete the request \"' + title + '\"? This is just a test.')) {
        alert('Delete function called successfully!\\nID: ' + requestId + '\\nTitle: ' + title);
    }
}
</script>";

echo "<h3>Check Console</h3>";
echo "<p>Open browser console (F12) and click the test buttons to see if JavaScript functions are working.</p>";
?>
