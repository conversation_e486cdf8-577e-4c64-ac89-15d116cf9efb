<?php
require_once 'config.php';

echo "<h2>URL Debug Information</h2>";
echo "<p><strong>Current Script:</strong> " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>HTTP Host:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

echo "<h3>Defined Constants:</h3>";
echo "<p><strong>SITE_URL:</strong> " . (defined('SITE_URL') ? SITE_URL : 'Not defined') . "</p>";
echo "<p><strong>BASE_URL:</strong> " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
echo "<p><strong>ADMIN_URL:</strong> " . (defined('ADMIN_URL') ? ADMIN_URL : 'Not defined') . "</p>";
echo "<p><strong>USER_URL:</strong> " . (defined('USER_URL') ? USER_URL : 'Not defined') . "</p>";

echo "<h3>Helper Functions:</h3>";
echo "<p><strong>get_base_url():</strong> " . get_base_url() . "</p>";
echo "<p><strong>get_admin_url():</strong> " . get_admin_url() . "</p>";

echo "<h3>Expected URLs:</h3>";
echo "<p><strong>Dashboard:</strong> " . get_base_url() . "/dashboard.php (should redirect to user/dashboard.php)</p>";
echo "<p><strong>Requests:</strong> " . get_base_url() . "/requests.php (should redirect to user/requests.php)</p>";
echo "<p><strong>Volunteer:</strong> " . get_base_url() . "/volunteer_opportunities.php (should redirect to user/volunteer_opportunities.php)</p>";
echo "<p><strong>Enhanced Donate:</strong> " . get_base_url() . "/enhanced_donate.php (should redirect to user/enhanced_donate.php)</p>";

echo "<h3>Test Links:</h3>";
echo "<p><a href='dashboard.php'>Dashboard</a> (should work with .htaccess redirect)</p>";
echo "<p><a href='requests.php'>Requests</a> (should work with .htaccess redirect)</p>";
echo "<p><a href='volunteer_opportunities.php'>Volunteer Opportunities</a> (should work with .htaccess redirect)</p>";
echo "<p><a href='user/dashboard.php'>Direct User Dashboard</a> (direct access)</p>";
echo "<p><a href='user/requests.php'>Direct User Requests</a> (direct access)</p>";
?>
