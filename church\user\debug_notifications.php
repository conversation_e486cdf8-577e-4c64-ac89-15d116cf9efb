<?php
session_start();
require_once '../config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please log in first.";
    exit;
}

$userId = $_SESSION['user_id'];

echo "<h2>Notification URLs Debug</h2>";

// Get sample notifications with action URLs
$stmt = $pdo->prepare("
    SELECT id, title, action_url, notification_type, created_at
    FROM notifications 
    WHERE recipient_id = ? 
    AND action_url IS NOT NULL 
    AND action_url != ''
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute([$userId]);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($notifications) {
    echo "<h3>Sample Notification URLs:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Title</th><th>Type</th><th>Action URL</th><th>Status</th></tr>";
    
    foreach ($notifications as $notification) {
        echo "<tr>";
        echo "<td>" . $notification['id'] . "</td>";
        echo "<td>" . htmlspecialchars($notification['title']) . "</td>";
        echo "<td>" . $notification['notification_type'] . "</td>";
        echo "<td>" . htmlspecialchars($notification['action_url']) . "</td>";
        
        // Check if URL is accessible
        $url = $notification['action_url'];
        $status = "Unknown";
        
        if (strpos($url, 'http') === 0) {
            $status = "Absolute URL";
        } elseif (strpos($url, '../admin/') === 0) {
            $status = "Admin relative URL";
        } elseif (strpos($url, 'admin/') === 0) {
            $status = "Admin URL (needs ../admin/)";
        } elseif (strpos($url, '/') === 0) {
            $status = "Root relative URL";
        } else {
            $status = "Relative URL (likely broken)";
        }
        
        echo "<td style='color: " . ($status === "Relative URL (likely broken)" ? "red" : "green") . ";'>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No notifications with action URLs found.</p>";
}

echo "<h3>Current Base URLs:</h3>";
echo "<p><strong>BASE_URL:</strong> " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
echo "<p><strong>ADMIN_URL:</strong> " . (defined('ADMIN_URL') ? ADMIN_URL : 'Not defined') . "</p>";
echo "<p><strong>Current page path:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";

echo "<h3>Recommendations:</h3>";
echo "<ul>";
echo "<li>Admin URLs should be prefixed with '../admin/' when viewed from user pages</li>";
echo "<li>Or use absolute URLs with the full domain</li>";
echo "<li>Or replace 'View Details' with a more appropriate action</li>";
echo "</ul>";
?>
